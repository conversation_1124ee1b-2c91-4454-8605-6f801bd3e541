import React from 'react';
import Divider from '@shape-construction/arch-ui/src/Divider';

export type DetailsListHeaderProps = {
  title: string;
};
export const DetailsListHeader: React.FC<DetailsListHeaderProps> = ({
  title
}) => {
  return (
    <div className="flex px-4 pt-4 justify-between items-center">
      <div className="text-xs leading-4 font-semibold tracking-wider uppercase text-gray-400">{title}</div>
    </div>
  );
};

export type DetailsListTextProps = {
  text: string;
  value?: string;
};
export const DetailsListText: React.FC<DetailsListTextProps> = ({
  text,
  value
}) => {
  return (
    <div
      className="flex px-4 py-2 gap-2 justify-between items-center"
    >
      <div
        className="text-sm leading-5 font-medium text-gray-700"
      >
        {text}
      </div>
      {value && (
        <div className="text-sm leading-5 font-medium text-neutral-subtlest">
          {value}
        </div>
      )}
    </div>
  );
};

export const IssueQualityDetails: React.FC = () => {
  return (
    <div className='max-h-[320px] max-w-[320px]'>
      <div className="px-4">
        <div className="flex py-2 justify-between items-center">
          <div className="text-sm leading-5 font-medium text-gray-700">
            Score criteria
          </div>
        </div>
      </div>
      <Divider orientation="horizontal" />

      <DetailsListHeader
        title="The basics"
      />
      <DetailsListText text="Description (At least 20 characters)" value="10%" />
      <DetailsListText text="Valid assignee or responsible person" value="10%" />
      <DetailsListText text="Last activity is within 4 weeks or if the issue is resolved." value="10%" />

      <Divider orientation='horizontal' />

      <DetailsListHeader
        title="Additional details"
      />
      <DetailsListText text="Valid location" value="10%" />
      <DetailsListText text="Impact is indicated" value="10%" />
      <DetailsListText text="Type / Sub-type is indicated" value="10%" />
      <DetailsListText text="Due date is indicated" value="5%" />
      <DetailsListText text="Discipline" value="5%" />
      <DetailsListText text="Includes a supporting attachment" value="20%" />
      <DetailsListText text="Last activity is within 2 weeks or if the issue is resolved." value="10%" />

      <Divider orientation='horizontal' />

      <DetailsListHeader
        title="Additional details"
      />
      <DetailsListText text="Valid location" value="10%" />
      <DetailsListText text="Impact is indicated" value="10%" />
      <DetailsListText text="Type / Sub-type is indicated" value="10%" />
      <DetailsListText text="Due date is indicated" value="5%" />
      <DetailsListText text="Discipline" value="5%" />
      <DetailsListText text="Includes a supporting attachment" value="20%" />
      <DetailsListText text="Last activity is within 2 weeks or if the issue is resolved." value="10%" />

      <Divider orientation='horizontal' />
      <DetailsListHeader
        title="Notes"
      />
      <DetailsListText text="An Issue remains visible in the heatmap for each week it is active (until resolved)." />
      <DetailsListText text="The score can change as more details are added or if the Issue becomes stale." />
    </div>
  );
};
