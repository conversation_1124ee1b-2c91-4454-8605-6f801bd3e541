import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import Badge from '@shape-construction/arch-ui/src/Badge';
import Button from '@shape-construction/arch-ui/src/Button';
import { InformationCircleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import * as PopoverMenu from '@shape-construction/arch-ui/src/PopoverMenu';
import { getHeatmapColorClasses, HEATMAP_LEVELS, HEATMAP_THEME } from './heatmap-config';
import { IssueQualityDetails } from './IssueQualityDetails';
import { Tooltip, TooltipContent, TooltipTrigger } from '@shape-construction/arch-ui/src/Tooltip/Tooltip';

export const HeatmapLegend = () => {
  const messages = useMessageGetter('dataBook.page.heatmapDashboard.healthLevels');
  return (
    <div className="overflow-x-auto">
      <div className="flex flex-row justify-between place-items-end px-4 min-w-max items-center">
        <PopoverMenu.Root>
          <PopoverMenu.Trigger>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button color="secondary" size="xxs" variant="outlined" leadingIcon={InformationCircleIcon}>
                  Scoring explained
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom" align="start">
                Click to see full details of the score criteria
              </TooltipContent>
            </Tooltip>
          </PopoverMenu.Trigger>

          <PopoverMenu.Content side="bottom" align="start" sideOffset={10}>
            <div className="md:max-h-50vh overflow-y-auto">
              <IssueQualityDetails />
            </div>
          </PopoverMenu.Content>
        </PopoverMenu.Root>
        <div className='flex'>
        {HEATMAP_LEVELS.slice(1).map((level) => {
          const badgeClasses = getHeatmapColorClasses(HEATMAP_THEME, level, 'badge');
          return (
            <div key={level} className="p-2 last:pr-0">
              <Badge label={messages(`${level}.label`)} className={badgeClasses} />
            </div>
          );
        })}
        </div>
      </div>
    </div>
  );
}