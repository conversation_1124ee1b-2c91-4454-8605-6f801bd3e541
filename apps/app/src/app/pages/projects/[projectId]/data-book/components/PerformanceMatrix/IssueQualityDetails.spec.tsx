import React from 'react';
import { render, screen } from 'tests/test-utils';
import { IssueQualityDetails, DetailsListHeader, DetailsListText, DetailsListProgressBar } from './IssueQualityDetails';

describe('<IssueQualityDetails />', () => {
  it('renders the main component with quality score sections', () => {
    render(<IssueQualityDetails />);

    expect(screen.getByText('dataBook.page.issueTracker.qualityDetails.currentScore')).toBeInTheDocument();
    expect(screen.getByText('Good')).toBeInTheDocument();
    expect(screen.getByText('dataBook.page.issueTracker.qualityDetails.completeness.title')).toBeInTheDocument();
    expect(screen.getByText('dataBook.page.issueTracker.qualityDetails.activity.title')).toBeInTheDocument();
    expect(screen.getByText('dataBook.page.issueTracker.qualityDetails.evidence.title')).toBeInTheDocument();
  });

  it('renders info description', () => {
    render(<IssueQualityDetails />);

    expect(screen.getByText('dataBook.page.issueTracker.qualityDetails.info.description')).toBeInTheDocument();
  });
});

describe('<DetailsListHeader />', () => {
  it('renders header with title and progress description', () => {
    render(
      <DetailsListHeader
        title="Test Section"
        progressPercentagesDescription="3/4"
        isComplete={false}
      />
    );

    expect(screen.getByText('Test Section')).toBeInTheDocument();
    expect(screen.getByText('3/4')).toBeInTheDocument();
  });

  it('applies correct styling when complete', () => {
    render(
      <DetailsListHeader
        title="Complete Section"
        progressPercentagesDescription="4/4"
        isComplete={true}
      />
    );

    const progressText = screen.getByText('4/4');
    expect(progressText).toHaveClass('text-green-500');
  });
});

describe('<DetailsListText />', () => {
  it('renders text with checked icon when complete', () => {
    render(<DetailsListText text="Test item" isComplete={true} />);

    expect(screen.getByText('Test item')).toBeInTheDocument();
    expect(screen.getByLabelText('checked progress item')).toBeInTheDocument();
  });

  it('renders text with unchecked icon when incomplete', () => {
    render(<DetailsListText text="Test item" isComplete={false} />);

    expect(screen.getByText('Test item')).toBeInTheDocument();
    expect(screen.getByLabelText('unchecked progress item')).toBeInTheDocument();
  });

  it('renders text without icon when isComplete is undefined', () => {
    render(<DetailsListText text="Test item" />);

    expect(screen.getByText('Test item')).toBeInTheDocument();
    expect(screen.queryByLabelText('checked progress item')).not.toBeInTheDocument();
    expect(screen.queryByLabelText('unchecked progress item')).not.toBeInTheDocument();
  });

  it('renders text with freeform text content', () => {
    render(<DetailsListText text="Test item" rightContent="Custom text" />);

    expect(screen.getByText('Test item')).toBeInTheDocument();
    expect(screen.getByText('Custom text')).toBeInTheDocument();
    expect(screen.queryByLabelText('checked progress item')).not.toBeInTheDocument();
    expect(screen.queryByLabelText('unchecked progress item')).not.toBeInTheDocument();
  });

  it('renders text with freeform number content', () => {
    render(<DetailsListText text="Test item" rightContent={42} />);

    expect(screen.getByText('Test item')).toBeInTheDocument();
    expect(screen.getByText('42')).toBeInTheDocument();
    expect(screen.queryByLabelText('checked progress item')).not.toBeInTheDocument();
    expect(screen.queryByLabelText('unchecked progress item')).not.toBeInTheDocument();
  });

  it('renders text with nothing when rightContent is null', () => {
    render(<DetailsListText text="Test item" rightContent={null} />);

    expect(screen.getByText('Test item')).toBeInTheDocument();
    expect(screen.queryByLabelText('checked progress item')).not.toBeInTheDocument();
    expect(screen.queryByLabelText('unchecked progress item')).not.toBeInTheDocument();
  });

  it('falls back to checkbox behavior when rightContent is undefined', () => {
    render(<DetailsListText text="Test item" isComplete={true} />);

    expect(screen.getByText('Test item')).toBeInTheDocument();
    expect(screen.getByLabelText('checked progress item')).toBeInTheDocument();
  });
});

describe('<DetailsListProgressBar />', () => {
  it('renders progress bar with correct progress', () => {
    render(
      <DetailsListProgressBar
        currentProgress={75}
        description="Test progress"
        isComplete={false}
      />
    );

    // The progress bar should be rendered (we can't easily test the actual progress value)
    const progressBar = screen.getByRole('progressbar');
    expect(progressBar).toBeInTheDocument();
  });

  it('uses success color when complete', () => {
    render(
      <DetailsListProgressBar
        currentProgress={100}
        description="Complete progress"
        isComplete={true}
      />
    );

    const progressBar = screen.getByRole('progressbar');
    expect(progressBar).toBeInTheDocument();
  });
});
